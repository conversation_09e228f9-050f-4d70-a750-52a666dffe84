const { decodeFieldsMeta<PERSON>rom<PERSON>son, createLabelToKeyMapping } = require('../../helper');

describe('decodeFieldsMetaFromJson', () => {
    const sampleMetaJson = JSON.stringify({
        translatedFields: [
            {
                key: 'field1',
                label: 'Field 1',
                required: true,
                widget: 'input',
                cust_component: null
            },
            {
                key: 'field2',
                label: 'Field 2',
                required: false,
                widget: 'select',
                options: [
                    { value: 'option1', label: 'Option 1' },
                    { value: 'option2', label: 'Option 2' }
                ]
            },
            {
                key: 'file_field',
                label: 'File Upload',
                cust_component: 'Files',
                required: true
            },
            {
                key: 'mic_field',
                label: 'Microphone',
                cust_component: 'WIFY_MIC'
            }
        ]
    });

    test('should decode meta fields correctly', () => {
        const result = decodeFieldsMetaFromJson(sampleMetaJson);
        
        expect(result).toHaveLength(2); // Should exclude file components by default
        expect(result[0]).toMatchObject({
            key: 'field1',
            label: 'Field 1',
            required: true,
            widget: 'input'
        });
        expect(result[1]).toMatchObject({
            key: 'field2',
            label: 'Field 2',
            required: false,
            widget: 'select'
        });
        expect(result[1].options).toHaveLength(2);
    });

    test('should include file components when getFiles is true', () => {
        const result = decodeFieldsMetaFromJson(sampleMetaJson, { getFiles: true });
        
        expect(result).toHaveLength(4); // Should include all fields
        const fileField = result.find(field => field.key === 'file_field');
        expect(fileField).toBeDefined();
        expect(fileField.cust_component).toBe('Files');
    });

    test('should handle empty or invalid JSON', () => {
        expect(decodeFieldsMetaFromJson('')).toEqual([]);
        expect(decodeFieldsMetaFromJson(null)).toEqual([]);
        expect(decodeFieldsMetaFromJson('invalid json')).toEqual([]);
    });

    test('should handle JSON without translatedFields', () => {
        const invalidJson = JSON.stringify({ someOtherField: 'value' });
        const result = decodeFieldsMetaFromJson(invalidJson);
        expect(result).toEqual([]);
    });
});

describe('createLabelToKeyMapping', () => {
    const sampleFields = [
        { key: 'field1', label: 'Field 1' },
        { key: 'field2', label: 'Field 2' },
        { key: 'field3', cust_component_value: 'Custom Field' }
    ];

    test('should create correct label to key mapping', () => {
        const mapping = createLabelToKeyMapping(sampleFields);
        
        expect(mapping).toEqual({
            'Field 1': 'field1',
            'Field 2': 'field2',
            'Custom Field': 'field3'
        });
    });

    test('should handle empty array', () => {
        const mapping = createLabelToKeyMapping([]);
        expect(mapping).toEqual({});
    });

    test('should handle invalid input', () => {
        const mapping = createLabelToKeyMapping(null);
        expect(mapping).toEqual({});
    });
});

describe('Integration test - decodeFieldsMetaFromJson with createLabelToKeyMapping', () => {
    test('should work together like in lambda functions', () => {
        const metaJson = JSON.stringify({
            translatedFields: [
                {
                    key: 'customer_name',
                    label: 'Customer Name',
                    required: true,
                    widget: 'input'
                },
                {
                    key: 'service_type',
                    label: 'Service Type',
                    required: true,
                    widget: 'select',
                    options: [
                        { value: 'installation', label: 'Installation' },
                        { value: 'repair', label: 'Repair' }
                    ]
                }
            ]
        });

        // Decode meta fields
        const decodedMeta = decodeFieldsMetaFromJson(metaJson);
        
        // Create label to key mapping (commonly used in lambdas)
        const labelToKeyMap = createLabelToKeyMapping(decodedMeta);
        
        // Verify the integration works as expected
        expect(labelToKeyMap['Customer Name']).toBe('customer_name');
        expect(labelToKeyMap['Service Type']).toBe('service_type');
        
        // Simulate lambda usage
        const allValues = {
            [labelToKeyMap['Customer Name']]: 'John Doe',
            [labelToKeyMap['Service Type']]: 'installation'
        };
        
        expect(allValues.customer_name).toBe('John Doe');
        expect(allValues.service_type).toBe('installation');
    });
});
