exports.handler = async (event) => {
  try {
    let message = "Error occured cannot convert json";
    const inputJson = event?.convertedData || {}; // incoming JSON
    console.log("inputJson: ", JSON.stringify(inputJson));

    // Find DELIVERY and INVOICE addresses
    const deliveryAddress = inputJson?.ServiceOrder?.Addresses?.Address?.find(
      (addr) => addr.Code === "DELIVERY"
    );
    const invoiceAddress = inputJson?.ServiceOrder?.Addresses?.Address?.find(
      (addr) => addr.Code === "INVOICE"
    );

    if (!deliveryAddress) {
      message = "DELIVERY address not found in input JSON";
      throw new Error("DELIVERY address not found in input JSON");
    }
    if (!invoiceAddress) {
      message = "INVOICE address not found in input JSON";
      throw new Error("INVOICE address not found in input JSON");
    }

    // Break Address1 into first part & rest for DELIVERY
    let cust_line_0 = "";
    let cust_line_1 = "";
    if (deliveryAddress.Address1) {
      const [line0, ...rest] = deliveryAddress.Address1.split(",");
      cust_line_0 = line0.trim();
      cust_line_1 = rest.join(",").trim();
    }

    // Combine Address2-5 into one string for DELIVERY
    const extraAddressParts = [
      deliveryAddress.Address2,
      deliveryAddress.Address3,
      deliveryAddress.Address4,
      deliveryAddress.Address5,
    ]
      .filter(Boolean)
      .join(", ");

    // Extract date from TimeWindowFrom
    const timeWindowFrom =
      inputJson?.ServiceOrder?.Services?.Service?.TimeWindows?.BookedTimeWindow
        ?.TimeWindowFrom;
    const formattedDate = timeWindowFrom
      ? new Date(timeWindowFrom).toISOString()
      : null;

    // Prepare INVOICE address fields (Address1–Address5 only)
    const invoiceAddressParts = [
      invoiceAddress.Address1,
      invoiceAddress.Address2,
      invoiceAddress.Address3,
      invoiceAddress.Address4,
      invoiceAddress.Address5,
    ].filter(Boolean);

    const now = process.hrtime.bigint();
    const microseconds = Number(now / 1000n);

    // Build the output JSON
    const outputJson = {
      cust_full_name: deliveryAddress.Name || null,
      cust_mobile: deliveryAddress.MobilePhone
        ? deliveryAddress.MobilePhone.slice(-10)
        : null,
      cust_email: deliveryAddress.Email || null,
      cust_pincode: deliveryAddress.PostalCode || null,
      OrderNumber: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
      cust_city: deliveryAddress.City || null,
      cust_state: deliveryAddress.State || null,
      cust_line_0,
      cust_line_1: cust_line_1 || null,
      cust_line_2: extraAddressParts || null,
      "639edfe2-2eb0-42b2-86b6-83a3547d3063": formattedDate,
      "abfe7d96-b9f7-4a2c-994a-cb5f6f169642": invoiceAddressParts,
      request_description: `Dummy description for IKEA B2C ${microseconds}`,
      request_priority: "Normal",
    };

    console.log("outputJson: ", JSON.stringify(outputJson));

    return {
      statusCode: 200,
      body: JSON.stringify(outputJson, null, 2),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message }),
    };
  }
};
