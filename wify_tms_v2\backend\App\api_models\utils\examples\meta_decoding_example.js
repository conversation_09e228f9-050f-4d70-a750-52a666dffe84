/**
 * Example usage of decodeFieldsMetaFromJson and createLabelToKeyMapping functions
 * This demonstrates how to use the backend equivalent of frontend's decodeFieldsMetaFrmJson
 */

const { decodeFieldsMetaFromJson, createLabelToKeyMapping } = require('../helper');

// Example meta JSON (similar to what comes from database)
const sampleMetaJson = JSON.stringify({
    translatedFields: [
        {
            key: 'customer_name',
            label: 'Customer Name',
            required: true,
            widget: 'input',
            cust_component: null
        },
        {
            key: 'service_type',
            label: 'Service Type',
            required: true,
            widget: 'select',
            options: [
                { value: 'installation', label: 'Installation' },
                { value: 'repair', label: 'Repair' },
                { value: 'maintenance', label: 'Maintenance' }
            ]
        },
        {
            key: 'priority_level',
            label: 'Priority Level',
            required: false,
            widget: 'radio',
            options: [
                { value: 'low', label: 'Low' },
                { value: 'medium', label: 'Medium' },
                { value: 'high', label: 'High' }
            ]
        },
        {
            key: 'attachment_field',
            label: 'Attachments',
            cust_component: 'Files',
            required: false
        },
        {
            key: 'voice_note',
            label: 'Voice Note',
            cust_component: 'WIFY_MIC',
            required: false
        }
    ]
});

function demonstrateBasicUsage() {
    console.log('=== Basic Usage Example ===');
    
    // Decode meta fields (excludes file components by default)
    const decodedMeta = decodeFieldsMetaFromJson(sampleMetaJson);
    console.log('Decoded meta fields:', decodedMeta.length);
    
    // Create label-to-key mapping for easy field access
    const labelToKeyMap = createLabelToKeyMapping(decodedMeta);
    console.log('Label to key mapping:', labelToKeyMap);
    
    // Example of how this would be used in a lambda function
    const allValues = {
        [labelToKeyMap['Customer Name']]: 'John Doe',
        [labelToKeyMap['Service Type']]: 'installation',
        [labelToKeyMap['Priority Level']]: 'high'
    };
    
    console.log('Form values using mapping:', allValues);
}

function demonstrateWithFileComponents() {
    console.log('\n=== Including File Components Example ===');
    
    // Include file components in the decoded meta
    const decodedMetaWithFiles = decodeFieldsMetaFromJson(sampleMetaJson, { 
        getFiles: true 
    });
    console.log('Decoded meta with files:', decodedMetaWithFiles.length);
    
    const labelToKeyMapWithFiles = createLabelToKeyMapping(decodedMetaWithFiles);
    console.log('Label to key mapping with files:', labelToKeyMapWithFiles);
}

function demonstrateLambdaStyleUsage() {
    console.log('\n=== Lambda Function Style Usage ===');
    
    // This simulates how it would be used in a lambda function like the ones in the codebase
    const meta = decodeFieldsMetaFromJson(sampleMetaJson);
    
    // Create label to key mapping (common pattern in lambdas)
    const labelToKeyMap = {};
    meta.forEach(singleField => {
        labelToKeyMap[singleField.label || singleField.cust_component_value] = singleField.key;
    });
    
    // Or use the helper function (recommended)
    const labelToKeyMapHelper = createLabelToKeyMapping(meta);
    
    console.log('Manual mapping:', labelToKeyMap);
    console.log('Helper mapping:', labelToKeyMapHelper);
    console.log('Mappings are equal:', JSON.stringify(labelToKeyMap) === JSON.stringify(labelToKeyMapHelper));
    
    // Simulate form manipulation (common in dynamic form logic)
    const manipulatedFieldValues = {};
    
    // Example: Set a default value
    manipulatedFieldValues[labelToKeyMap['Priority Level']] = 'medium';
    
    // Example: Conditional logic based on service type
    const serviceTypeValue = 'installation';
    if (serviceTypeValue === 'installation') {
        manipulatedFieldValues[labelToKeyMap['Customer Name']] = 'Installation Customer';
    }
    
    console.log('Manipulated field values:', manipulatedFieldValues);
}

function demonstrateErrorHandling() {
    console.log('\n=== Error Handling Example ===');
    
    // Test with invalid JSON
    const invalidJson = 'invalid json string';
    const result1 = decodeFieldsMetaFromJson(invalidJson);
    console.log('Invalid JSON result:', result1);
    
    // Test with empty string
    const result2 = decodeFieldsMetaFromJson('');
    console.log('Empty string result:', result2);
    
    // Test with null
    const result3 = decodeFieldsMetaFromJson(null);
    console.log('Null result:', result3);
    
    // Test with JSON missing translatedFields
    const jsonWithoutFields = JSON.stringify({ someOtherProperty: 'value' });
    const result4 = decodeFieldsMetaFromJson(jsonWithoutFields);
    console.log('JSON without translatedFields result:', result4);
}

// Run all examples
if (require.main === module) {
    demonstrateBasicUsage();
    demonstrateWithFileComponents();
    demonstrateLambdaStyleUsage();
    demonstrateErrorHandling();
}

module.exports = {
    demonstrateBasicUsage,
    demonstrateWithFileComponents,
    demonstrateLambdaStyleUsage,
    demonstrateErrorHandling,
    sampleMetaJson
};
