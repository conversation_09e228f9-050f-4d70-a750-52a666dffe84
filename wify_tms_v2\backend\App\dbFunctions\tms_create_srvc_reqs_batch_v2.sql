CREATE OR REPLACE FUNCTION public.tms_create_srvc_reqs_batch_v2(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
	-- Bare minimums
	status boolean;
	message text;
	affected_rows integer;
	validation_resp text[];
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
	srvc_type_id_ integer;
	srvc_pvdr_id_ integer;
	srvc_config_data_ json;
	-- Form data
	batch_data_ json;
	
	-- temp
  	_single_entry json;
  	ins_id bigint;
  	_form_data_proto json;
  	_single_entry_creation_resp json;
  	exception_hint text;
    _form_data_proto_json json;
  	_temp_single_order_data jsonb;
  	_temp_form_proto_fr_get_order_details jsonb;
  	_temp_is_api_call int;
  	_temp_existing_req_id_fr_api_call bigint;
  	_temp_other_unique_id_api text;
  	_for_deletion bool;
    _entry_ids_vs_query json default '{}';
  	_temp_existing_srvc_pvdr_id_fr_api_call bigint;
  	row_count_ int;
	srvc_prvdr_valitation_resp_ json;
	pincode_length_validation_resp_ json;
	_cust_pincode_ text;
	_sbtsk_entry_ids_vs_query_fr_deletion jsonb default '{}';
	_temp_is_bulk_update int;
	_temp_is_agent_update bool;
	_temp_is_bulk_line_items_update int;
	_temp_existing_req_id_fr_bulk_update bigint;

	-- Output
	ins_ids bigint[];
    cust_org_id int;
   	api_call_resp json default '[]';
   	existing_req_ids_fr_api_call bigint[];
    validation_resp_ json;
	org_level_settings_data json;
	_config_data json;
	srvc_type_title_ text;
	brand_org_id_ integer;
	customer_access_ int default 0;
	srvc_status_title_ text;
	srvc_status_key_ text;
	org_name_ text;		

    -- New additions for partial failure reporting
    failed_entries jsonb default '[]';
    success_entries jsonb default '[]';
   
begin
	status = false;
	message = 'Internal_error';

	org_id_ = json_extract_path_text(form_data_,'org_id');
	usr_id_ = json_extract_path_text(form_data_,'usr_id');
	ip_address_ = json_extract_path_text(form_data_,'ip_address');
	user_agent_ = json_extract_path_text(form_data_,'user_agent');
	srvc_type_id_ = json_extract_path_text(form_data_,'srvc_type_id');
	_for_deletion = form_data_->>'for_deletion';
--	raise notice 'Service id %',srvc_type_id_;
	-- Form data
	batch_data_ = form_data_->'batch_data';
	_form_data_proto = form_data_::jsonb - 'batch_data';
	_temp_is_api_call = (form_data_->>'is_api_call')::int;
	_temp_is_bulk_update = (form_data_->>'is_bulk_update')::int;
	_temp_is_bulk_line_items_update = (form_data_->>'is_bulk_line_items_update')::int;
	row_count_ = 0;
	srvc_type_title_ := form_data_->>'provided_srvc_type_name';
	srvc_status_title_ := form_data_->>'provided_new_status';
	org_name_ := form_data_->>'provided_brand_name';
	_temp_is_agent_update = (form_data_->>'is_agent_update')::bool;
	
	if tms_hlpr_is_org_srvc_prvdr(org_id_) then
		customer_access_ = 1;
	end if;

	if srvc_type_id_ is null then
		--get the org id of the specified org name 
		select orgs_.org_id
		  from cl_tx_orgs as orgs_
		 where orgs_.nickname = org_name_		   
		  into brand_org_id_;
		 
		--get the srvc type id of the specified srvc type name 
		select srvc_types_.service_type_id  
		  from cl_cf_service_types as srvc_types_
		 where srvc_types_.title = srvc_type_title_		
		   and srvc_types_.org_id = brand_org_id_
		  into srvc_type_id_; 
	end if;
	
	if srvc_status_title_ is not null then
		--get the srvc status key of the specified srvc status title  
		select srvc_status.status_key 
		  from cl_cf_srvc_statuses as srvc_status
		 where srvc_status.srvc_id = srvc_type_id_
		   and srvc_status.title = srvc_status_title_
		  into srvc_status_key_; 
	end if;

	--get config_data by srvc_type_id
    select srvc_type.form_data 
  	  from cl_cf_service_types as srvc_type 
     where service_type_id = srvc_type_id_ 
  	  into _config_data;
  	 
	--Get org_level_settings_data by org_id
	org_level_settings_data = tms_hlpr_get_org_level_settings_config_data_for_org(org_id_)->'data';
	raise notice 'brand_org_id_ %',brand_org_id_;
	raise notice 'srvc_type_id_ %',srvc_type_id_;
	raise notice 'srvc_status_key_ %',srvc_status_key_;
	for _single_entry in select * from json_array_elements(batch_data_)
	loop
		begin
			_form_data_proto_json = '{}'::jsonb;
			_form_data_proto_json = _form_data_proto::jsonb || _single_entry::jsonb;
			_form_data_proto_json = jsonb_set(_form_data_proto_json::jsonb,'{srvc_type_id}',to_jsonb(srvc_type_id_),true);
			_form_data_proto_json = jsonb_set(_form_data_proto_json::jsonb,'{is_customer_access}',to_jsonb(customer_access_),true);
			_form_data_proto_json = jsonb_set(_form_data_proto_json::jsonb,'{is_agent_update}',to_jsonb(_temp_is_agent_update),true);
		
			row_count_ = row_count_ + 1;
		
			raise notice 'row_count_ %',row_count_;
			raise notice '_form_data_proto_json %',_form_data_proto_json;

			-- Check for missing_fields array and skip if present and not empty
			if _single_entry ? 'missing_fields' and jsonb_array_length(_single_entry->'missing_fields') > 0 then
				failed_entries := failed_entries || jsonb_build_array(jsonb_build_object(
					'index', row_count_,
					'display_code', _single_entry->>'tms_display_code',
					'error', 'missing required fields'
				));
				continue;
			end if;

			if _temp_is_bulk_update > 0 then
				if srvc_status_key_ is not null then
					_form_data_proto_json = jsonb_set(_form_data_proto_json::jsonb,'{new_status}',to_jsonb(srvc_status_key_),true);
				end if;
				raise notice '_single_entry %',_single_entry;
				if _single_entry->>'srvc_req_id' is null then
					select srvc_req.db_id from cl_tx_srvc_req as srvc_req
					where (
							srvc_req.org_id = org_id_
							or
							srvc_req.org_id = brand_org_id_
							)
					  and srvc_req.srvc_type_id = srvc_type_id_
					  and srvc_req.display_code = _single_entry->>'tms_display_code'
					group by srvc_req.db_id
					limit 1
					into _temp_existing_req_id_fr_bulk_update;
				else
					_temp_existing_req_id_fr_bulk_update = _single_entry->'srvc_req_id';
				end if;
			
				raise notice '_temp_existing_req_id_fr_bulk_update %',_temp_existing_req_id_fr_bulk_update;
				raise notice '_form_data_proto_json %',_form_data_proto_json;
				if _temp_existing_req_id_fr_bulk_update > 0 then
					_single_entry_creation_resp = tms_create_service_request(_form_data_proto_json,_temp_existing_req_id_fr_bulk_update::int);
				else
					failed_entries := failed_entries || jsonb_build_array(jsonb_build_object(
						'index', row_count_,
						'display_code', _single_entry->>'tms_display_code',
						'error', 'Invalid request id'
					));
					continue;
				end if;
			else
				_single_entry_creation_resp = tms_create_service_request(_form_data_proto_json);
			end if;
		
			perform pg_sleep(0.000001);-- wait 1 microsecond 	
			
			if _single_entry_creation_resp->'status' then
				ins_ids := ins_ids || (_single_entry_creation_resp->'data'#>>'{entry_id}')::bigint;
				success_entries := success_entries || jsonb_build_array(jsonb_build_object(
					'index', row_count_,
					'display_code', _single_entry->>'tms_display_code',
					'entry_id', (_single_entry_creation_resp->'data'#>>'{entry_id}')
				));

				cust_org_id = (_single_entry_creation_resp->'data'#>>'{cust_org_id}')::int;
				_entry_ids_vs_query := _entry_ids_vs_query::jsonb || json_build_object((_single_entry_creation_resp->'data'#>>'{entry_id}'), _form_data_proto_json)::jsonb;
			else
				failed_entries := failed_entries || jsonb_build_array(jsonb_build_object(
					'index', row_count_,
					'display_code', _single_entry->>'tms_display_code',
					'error', _single_entry_creation_resp->>'code'
				));
			end if;
		exception when others then
			failed_entries := failed_entries || jsonb_build_array(jsonb_build_object(
				'index', row_count_,
				'display_code', _single_entry->>'tms_display_code',
				'error', sqlerrm
			));
			continue;
		end;
	end loop;
	
	status = true;
	message = 'success';

	resp_data = json_build_object(
		'entry_ids', ins_ids,
		'cust_org_id', cust_org_id,
		'entry_ids_vs_query', _entry_ids_vs_query,
		'success_entries', success_entries,
		'failed_entries', failed_entries,
		'srvc_type_id',srvc_type_id_
	);

	return json_build_object('status',status,'code',message,'data',resp_data);
 
END;
$function$
;
