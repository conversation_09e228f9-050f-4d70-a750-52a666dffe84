var HttpStatus = require('http-status-codes');
const validate = require('../api_models/utils/antdRuleValidator');
const isEmpty = require('is-empty');
const _ = require('lodash');
const moment = require('moment');

//translations is an object which contains api params as key and the values are the keys that are used in DataBase function
const translations = {
    tms_display_code: 'tms_display_code',
    ext_order_id: 'ext_order_id',
    service_provider_id: 'service_provider_id',
};

const keyVsLabel = {
    cust_mobile: 'Mobile no',
    cust_full_name: 'Name',
    cust_line_0: 'Flat no',
    cust_line_1: 'Building/Apartment name',
    cust_line_2: 'Line 1',
    cust_pincode: 'Pincode',
    cust_city: 'City',
    cust_state: 'State',
    request_description: 'Description',
    request_req_date: 'Req. Service Date',
    start_time: 'Start Time',
    end_time: 'End Time',
    request_priority: 'Priority',
    service_provider_id: 'Service provider',
};

const getReverseMappingOfApiParams = () => _.invert(translations);

//subtask upadation meta
const serviceRequestDeletionMeta = [
    {
        key: 'srvc_type_id',
        label: 'Service type ID',
        widget: 'number',
        required: true,
    },
    {
        key: 'tms_display_code',
        label: 'TMS display code',
        required: true,
    },
    {
        key: 'ext_order_id',
        label: 'Ext order ID',
        required: true,
    },
];

const srvcPrvdrAssignmentToSrvcReqMeta = [
    {
        key: 'srvc_type_id',
        label: 'Service type ID',
        widget: 'number',
        required: true,
    },
    {
        key: 'tms_display_code',
        label: 'TMS Display Code',
        required: true,
    },
    {
        key: 'ext_order_id',
        label: 'Ext order ID',
        required: true,
    },
    {
        key: 'service_provider_id',
        label: 'Service provider ID',
        required: true,
    },
];

/**
 * Creates a mapping of keys to labels from an array of objects.
 *
 * @param {Array<{ key: string, label: string }>} data - An array of objects containing `key` and `label` properties.
 * @returns {Object} An object where keys are extracted from `key` properties and values from `label` properties.
 */
const createKeyVsLabelMap = (data) => {
    return data.reduce((acc, { key, label }) => {
        acc[key] = label;
        return acc;
    }, {});
};

/**
 * Validates batch data by checking for missing or empty values in the given batch data.
 *
 * @param {Object} params - The function parameters.
 * @param {Object} params.singleBatchData - The batch data to validate, with key-value pairs.
 * @param {Array<Object>} params.customFields - An array of custom field objects used to generate a key-label mapping.
 *
 * @returns {Array<Object>} An array of error objects, where each object contains missing fields and corresponding error messages.
 */
const validateBatchData = ({ singleBatchData, customFields, staticFields }) => {
    const errors = [];
    let customFieldsKeyVsLabel;
    if (customFields?.length > 0) {
        customFieldsKeyVsLabel = createKeyVsLabelMap(customFields);
    }

    // Create a lookup object for required fields
    const requiredFields = {};
    customFields.forEach((field) => {
        if (field.required) {
            requiredFields[field.key] = true;
        }
    });

    staticFields.forEach((field) => {
        if (field.required) {
            requiredFields[field.key] = true;
        }
    });

    if (singleBatchData) {
        const errorObj = { errors: [] };

        Object.keys(singleBatchData).forEach((key) => {
            if (!requiredFields[key]) {
                return;
            }
            if (
                singleBatchData[key] == null ||
                singleBatchData[key] == undefined ||
                singleBatchData[key] == '' ||
                (typeof singleBatchData[key] == 'string' &&
                    singleBatchData[key].trim() == '')
            ) {
                errorObj[key] = undefined; // Mark the missing fields
                errorObj.errors.push(
                    `${keyVsLabel[key] || customFieldsKeyVsLabel[key]} must not be null or empty`
                );
            }
        });

        if (errorObj.errors.length > 0) {
            errors.push(errorObj);
        }
    }

    return errors;
};

/**
 * Retrieves an array of mandatory configuration keys based on the given service type configuration data.
 *
 * @param {Object} params - The function parameters.
 * @param {Object} params.srvcTypeConfigData - The service type configuration data.
 * @param {boolean} [params.srvcTypeConfigData.srvc_cust_mobile_not_mandatory] - Indicates if the customer mobile number is not mandatory.
 * @param {boolean} [params.srvcTypeConfigData.srvc_req_cust_pincode_mandatory] - Indicates if the customer pincode is mandatory.
 * @param {boolean} [params.srvcTypeConfigData.request_service_date_mandatory_while_request_creation] - Indicates if the request service date is mandatory while creating a request.
 *
 * @returns {string[]} An array of mandatory keys.
 */
const getMandatoryConfigKeys = ({ srvcTypeConfigData }) => {
    let mandatoryKeys = [];
    if (srvcTypeConfigData) {
        if (!srvcTypeConfigData?.srvc_cust_mobile_not_mandatory) {
            mandatoryKeys.push('cust_mobile');
        }
        if (
            Array.isArray(
                srvcTypeConfigData?.select_mandatory_address_fields_for_a_request
            )
                ? srvcTypeConfigData?.select_mandatory_address_fields_for_a_request.includes(
                      'cust_pincode'
                  )
                : srvcTypeConfigData?.srvc_req_cust_pincode_mandatory
        ) {
            mandatoryKeys.push('cust_pincode');
        }
        if (
            srvcTypeConfigData?.request_service_date_mandatory_while_request_creation
        ) {
            mandatoryKeys.push('request_req_date');
        }
    }
    return mandatoryKeys;
};

/**
 * Retrieves custom fields from the service type configuration data.
 *
 * @param {Object} params - The function parameters.
 * @param {Object} params.srvcTypeConfigData - The service type configuration data.
 * @param {string} [params.srvcTypeConfigData.srvc_cust_fields_json] - A JSON string containing custom field data.
 *
 * @returns {Array<Object>} An array of translated custom fields, or an empty array if no data is available.
 */
const getCustomFields = ({ srvcTypeConfigData }) => {
    if (srvcTypeConfigData?.srvc_cust_fields_json) {
        return JSON.parse(srvcTypeConfigData?.srvc_cust_fields_json)
            ?.translatedFields;
    }
    return [];
};

/**
 * Generates metadata for service request custom fields.
 *
 * @param {Object} params - The function parameters.
 * @param {Array<{ key: string, label: string, required: boolean }>} params.customFields - An array of custom field objects.
 * @param {boolean} params.is_srvc_req_creation - Indicates whether the service request is being created.
 *
 * @returns {Array<{ key: string, label: string, required: boolean }>|undefined} An array of custom field metadata, or undefined if no custom fields exist.
 */
const getServiceRequestCustomFieldMeta = ({
    customFields,
    is_srvc_req_creation,
}) => {
    if (customFields.length > 0) {
        return customFields.map(({ key, label, required }) => ({
            key,
            label,
            required: required && is_srvc_req_creation,
        }));
    }
    return;
};

/**
 * Merges an array of validation error objects into a single consolidated error object.
 *
 * @param {Array<Array<Object>>} errorsList - An array containing arrays of validation error objects.
 * @returns {Array<Object>} An array with a single merged error object, or an empty array if there are no errors.
 *
 * @example
 * const errors = [
 *   [{ field1: "value1", errors: ["Error 1"] }],
 *   [{ field2: "value2", errors: ["Error 2"] }]
 * ];
 * const mergedErrors = mergeValidationErrors(errors);
 * console.log(mergedErrors);
 * // Output: [{ field1: "value1", field2: "value2", errors: ["Error 1", "Error 2"] }]
 */
const mergeValidationErrors = (errorsList) => {
    const allErrors = errorsList.flat().filter(Boolean); // Flatten and remove falsy values

    if (!allErrors.length) return []; // Return early if no errors

    return [
        allErrors.reduce(
            (acc, { errors, ...rest }) => {
                Object.assign(acc, rest);
                acc.errors.push(...errors);
                return acc;
            },
            { errors: [] }
        ),
    ];
};

const getServiceRequestCreationMeta = ({
    is_srvc_req_creation,
    mandatoryKeysAsPerConfig,
}) => {
    let isRequired = is_srvc_req_creation ? true : false;
    const serviceRequestCreationMeta = [
        {
            key: 'cust_full_name',
            label: 'Full Name',
            rules: [
                {
                    type: 'string',
                },
            ],
            required: isRequired,
        },
        {
            key: 'cust_mobile',
            label: 'Mobile',
            rules: [
                {
                    type: 'string',
                },
            ],
            required:
                isRequired && mandatoryKeysAsPerConfig.includes('cust_mobile'),
        },
        {
            key: 'cust_pincode',
            label: 'Pincode',
            required:
                isRequired && mandatoryKeysAsPerConfig.includes('cust_pincode'),
        },
        {
            key: 'request_description',
            label: 'Description',
            rules: [
                {
                    type: 'string',
                },
            ],
            required: isRequired,
        },
        {
            key: 'request_priority',
            label: 'Request Priority',
            options: ['Urgent', 'High', 'Normal', 'Low', 'None'],
            required: isRequired,
        },
        {
            key: 'request_req_date',
            label: 'Request Date',
            widget: 'date-picker',
            required:
                isRequired &&
                mandatoryKeysAsPerConfig.includes('request_req_date'),
            rules: [
                {
                    validator: (rule, value) => {
                        const selectedDate = moment(value);
                        const minDate = moment()
                            .subtract(10, 'years')
                            .startOf('day'); // 10 years ago, start of day
                        const maxDate = moment().add(2, 'months').endOf('day'); // Two months from today, end of day
                        return selectedDate.isBetween(
                            minDate,
                            maxDate,
                            'day',
                            '[]'
                        ); // Check if selected date is between five years ago and two months from today
                    },
                    message: 'Please select a date within the next two months!',
                    // message: 'Please select a date within the past 5 years or the next two months!'
                },
            ],
        },
        {
            key: 'creation_date',
            label: 'Creation Date',
            widget: 'date-picker',
            rules: [
                {
                    validator: (rule, value) => {
                        const selectedDate = moment(value);
                        const today = moment();
                        return selectedDate.isSameOrBefore(today, 'day'); // Check if selected date is same day or before today
                    },
                    message: 'Please select a date not after today!',
                },
            ],
        },
    ];

    return serviceRequestCreationMeta;
};

const getServiceRequestDeletionMeta = (data) => {
    const duplicateserviceRequestDeletionMeta = _.cloneDeep(
        serviceRequestDeletionMeta
    );
    // write conditional required fields here based on entered data
    if (data['tms_display_code']) {
        modifyPropsOfFieldMeta(
            duplicateserviceRequestDeletionMeta,
            'ext_order_id',
            'required',
            false
        );
    }
    if (data['ext_order_id']) {
        modifyPropsOfFieldMeta(
            duplicateserviceRequestDeletionMeta,
            'tms_display_code',
            'required',
            false
        );
    }
    return duplicateserviceRequestDeletionMeta;
};

const getSrvcPrvdrAssigMeta = (data) => {
    const duplicateSrvcPrvdrAssignmentToSrvcReqMeta = _.cloneDeep(
        srvcPrvdrAssignmentToSrvcReqMeta
    );
    // write conditional required fields here based on entered data
    if (data['tms_display_code']) {
        modifyPropsOfFieldMeta(
            duplicateSrvcPrvdrAssignmentToSrvcReqMeta,
            'ext_order_id',
            'required',
            false
        );
    }
    if (data['ext_order_id']) {
        modifyPropsOfFieldMeta(
            duplicateSrvcPrvdrAssignmentToSrvcReqMeta,
            'tms_display_code',
            'required',
            false
        );
    }
    return duplicateSrvcPrvdrAssignmentToSrvcReqMeta;
};
const validateServiceRequestDeletionInputFrBulk = (req, res, next) => {
    try {
        // Check if batch data is an array
        const batchData = Array.isArray(req.body.batch_data)
            ? req.body.batch_data
            : res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                  status: false,
                  message: 'invalid batch data',
                  data: null,
              });
        let errMsg; //To send single line error message.
        // Validate each object in batchData array
        const validationErrors = batchData.reduce((acc, data, index) => {
            // translate date
            const translatedData = translateAPIParamsToFrontendKeys(data);
            const errors = validate(
                getServiceRequestDeletionMeta(translatedData),
                translatedData
            );
            // If there are validation errors
            if (!isEmpty(errors)) {
                // Generate error message with object index
                if (isEmpty(errMsg)) {
                    //only first errors generated get stored in errMsg variable.
                    errMsg = `Error encountered at object ${(index =
                        index + 1)} ,${errors[0].errors.join()}]}`;
                }
                acc.push(...errors);
            }
            return acc;
        }, []);
        // If there are validation errors
        if (validationErrors.length > 0) {
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: errMsg,
                data: validationErrors,
            });
        }
        next();
    } catch (error) {
        console.log('Error in validateSubtaskCreationInputFrBulk', error);
        return res.status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Internal server error',
            data: error,
        });
    }
};

const validateSrvcProviderAssignmentInputFrBulk = (req, res, next) => {
    // console.log('validateSrvcProviderAssignmentInputFrBulk called');
    try {
        // Check if batch data is an array
        const batchData = Array.isArray(req.body.batch_data)
            ? req.body.batch_data
            : res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                  status: false,
                  message: 'invalid batch data',
                  data: null,
              });

        let errMsg; //To send single line error message.

        // Validate each object in batchData array
        const validationErrors = batchData.reduce((acc, data, index) => {
            // translate data(api params) to Frontend keys
            const translatedData = translateAPIParamsToFrontendKeys(data);

            const errors = validate(
                getSrvcPrvdrAssigMeta(translatedData),
                translatedData
            );
            // If there are validation errors
            if (!isEmpty(errors)) {
                // Generate error message with object index
                if (isEmpty(errMsg)) {
                    //only first errors generated get stored in errMsg variable.
                    errMsg = `Error encountered at object ${(index =
                        index + 1)}, ${errors[0].errors.join()}]}`;
                }
                acc.push(...errors);
            }
            return acc;
        }, []);

        // If there are validation errors
        if (validationErrors.length > 0) {
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: errMsg,
                data: validationErrors,
            });
        }
        next();
    } catch (error) {
        console.log(
            'Error in validateSrvcProviderAssignmentInputFrBulk',
            error
        );
        return res.status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Internal server error',
            data: error,
        });
    }
};
//This method is used to modify the props of the Single fields meta
const modifyPropsOfFieldMeta = (meta, key, prop, propValue) => {
    meta.forEach((singleFieldMeta) => {
        if (singleFieldMeta.key == key) {
            singleFieldMeta[prop] = propValue;
        }
    });
};

const validateSrvcReqCreationInputFrBulk = async (
    req,
    res,
    next,
    brand_model
) => {
    try {
        if (!Array.isArray(req.body.batch_data)) {
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: 'Invalid batch data',
                data: null,
            });
        }

        const batchData = req.body.batch_data;

        let errMsg; //To send single line error message.

        // Validate each object in batchData array
        // const validationErrors = batchData.reduce((acc, data, index) => {
        //     let is_srvc_req_updation = false;
        //     if(data?.tms_display_code == undefined){
        //         is_srvc_req_updation = true;
        //     }
        //     // translate data(api params) to Frontend keys
        //     const translatedData = translateAPIParamsToFrontendKeys(data);
        //     // console.log("translatedData>>",translatedData);
        //     const errors = validate(getServiceRequestCreationMeta(is_srvc_req_updation), translatedData);
        //     // If there are validation errors
        //     if (!isEmpty(errors)) {
        //     // Generate error message with object index
        //     if (isEmpty(errMsg)) { //only first errors generated get stored in errMsg variable.
        //         errMsg = `Error encountered at object ${(index =
        //         index + 1)}, ${errors[0].errors.join()}]}`;
        //     }
        //     acc.push(...errors);
        //     }
        //     return acc;
        // }, []);

        //We can't use async here that is the reason we are using forlop here
        let validationErrors = [];
        for (let index = 0; index < batchData.length; index++) {
            let data = batchData[index];
            let collab_order_id_ =
                data?.['79a88c7b-c64f-46c4-a277-bc80efa1c154'];

            let is_collab_order_id_exists = collab_order_id_
                ? await brand_model.validateCollabOrderidIsExists(
                      req,
                      collab_order_id_
                  )
                : false;
            const srvcTypeConfigData =
                await brand_model.getSrvcTypeConfigData(req);

            if (!srvcTypeConfigData) {
                return res
                    .status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
                    .send({
                        status: false,
                        message: 'Invalid Srvc type id',
                        data: [],
                    });
            }
            let is_srvc_req_creation = false;
            let errors;

            if (
                data?.tms_display_code == undefined &&
                is_collab_order_id_exists == false
            ) {
                is_srvc_req_creation = true;
            }
            // translate data(api params) to Frontend keys
            const translatedData = translateAPIParamsToFrontendKeys(data);
            // console.log("translatedData>>",translatedData);
            const mandatoryConfig = getMandatoryConfigKeys({
                srvcTypeConfigData,
            });

            const customFields = getCustomFields({ srvcTypeConfigData });

            errors = validate(
                getServiceRequestCreationMeta({
                    is_srvc_req_creation,
                    mandatoryKeysAsPerConfig: mandatoryConfig,
                }),
                translatedData
            );
            const customFieldErrors = validate(
                getServiceRequestCustomFieldMeta({
                    customFields,
                    is_srvc_req_creation,
                }),
                translatedData
            );
            const emptyOrNullErrors = validateBatchData({
                singleBatchData: translatedData,
                customFields,
                staticFields: getServiceRequestCreationMeta({
                    is_srvc_req_creation: true,
                    mandatoryKeysAsPerConfig: mandatoryConfig,
                }),
            });

            const mergedErrors = mergeValidationErrors([
                errors,
                customFieldErrors,
                emptyOrNullErrors,
            ]);

            // If there are validation errors
            if (!isEmpty(mergedErrors)) {
                // Generate error message with object index
                if (isEmpty(errMsg)) {
                    //only first errors generated get stored in errMsg variable.
                    errMsg = `Error encountered at object ${(index =
                        index + 1)}, ${mergedErrors[0].errors.join()}]}`;
                }

                validationErrors.push(...mergedErrors);
            }
        }

        // If there are validation errors
        if (validationErrors.length > 0) {
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: errMsg,
                data: validationErrors,
            });
        }
        next();
    } catch (error) {
        console.log('Error in validateSrvcReqCreationInputFrBulk', error);
        return res.status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Internal server error',
            data: error,
        });
    }
};
const translateAPIParamsToFrontendKeys = (apiParams) => {
    let translatedAPIParams = {};
    Object.keys(apiParams).map((singleParamkey) => {
        translatedAPIParams[translations[singleParamkey] || singleParamkey] =
            apiParams[singleParamkey];
    });
    return translatedAPIParams;
};

//This method translate the api batch data keys to the data base batch data keys to create subtask
//for example {"batch_data":[{"srvc_type_id":20,"sbtsk_ticket_id":"FIRS230428942191","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","sbtsk_type_id":3,"sbtsk_assignee":"65ef738a-5791-432d-9188-16bce946b3aa","sbtsk_priority":"Normal","sbtsk_start_day":"2023-05-08","sbtsk_start_time":"12:15PM","sbtsk_end_time":"12:30PM","sbtsk_remarks":"test"}]}
const translatedInputBatchDataKeysToRequired = (query, batch_data) => {
    let modifiedBatchData = [];

    if (Array.isArray(batch_data) && batch_data.length > 0) {
        batch_data.forEach((singleInputData) => {
            modifiedBatchData.push(
                translateAPIParamsToFrontendKeys(singleInputData)
            );
        });
    }

    query['batch_data'] = modifiedBatchData;
};

module.exports = {
    translateAPIParamsToFrontendKeys,
    translatedInputBatchDataKeysToRequired,
    validateServiceRequestDeletionInputFrBulk,
    validateSrvcProviderAssignmentInputFrBulk,
    validateSrvcReqCreationInputFrBulk,
};
