CREATE OR REPLACE FUNCTION public.tms_get_srvc_reqs_to_update_fr_va(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	resp_data_ json;
	batch_data_ json;
	assgnd_org_ids integer[];
	org_name_ text;
	srvc_type_title_ text;
	srvc_status_title_ text;
	modified_form_data_ json;
	org_id_ integer;
	srvc_type_id_ integer;
	srvc_status_key_ text;
	usr_id_ uuid;
	logged_in_org_id_ integer;
	customer_access_ int default 0;
	is_frontend_ text;
	matching_ids_json json;
	matching_ids bigint[];
	temp_id bigint;
	filter_ json;
	filter_creation_date text[];
	filter_from_date timestamp;
	filter_to_date  timestamp;
	srvc_type_id_vs_authorities json;
  	usr_has_access_to_srvc_requests json;
  	usr_has_authority_fr_srvc_requests json;
  	filter_locations text[];
	is_org_srvc_prvdr_ boolean;
	custom_fields_config_data json;

	begin
		modified_form_data_ = '{}';
	
		logged_in_org_id_ := form_data_->>'org_id';
		org_name_ := form_data_->>'provided_brand_name';
		srvc_type_title_ := form_data_->>'provided_srvc_type_name';
		srvc_status_title_ := form_data_->>'provided_current_status';
		usr_id_ := form_data_->>'usr_id';
		is_frontend_ = form_data_->>'is_frontend';
		filter_ = form_data_->'filters';

		is_org_srvc_prvdr_ = tms_hlpr_is_org_srvc_prvdr(logged_in_org_id_);
	
		usr_has_access_to_srvc_requests = (tms_hlpr_usr_has_access_to_srvc_requests(usr_id_,logged_in_org_id_)::json) ;
	    usr_has_authority_fr_srvc_requests = (tms_hlpr_usr_has_authority_fr_srvc_requests(usr_id_,logged_in_org_id_));
	 	srvc_type_id_vs_authorities = tms_hlpr_get_srvc_type_id_vs_authorities(logged_in_org_id_);
	 	filter_locations = tms_get_assigned_loc_to_user(usr_id_);
 
	
		--get the org id of the specified org name 
		select orgs_.org_id
		  from cl_tx_orgs as orgs_
		 where orgs_.nickname = org_name_		   
		  into org_id_; 
		 
		--get the srvc type id of the specified srvc type name 
		select srvc_types_.service_type_id  
		  from cl_cf_service_types as srvc_types_
		 where srvc_types_.title = srvc_type_title_		
		   and srvc_types_.org_id = org_id_
		  into srvc_type_id_; 
		 
		--get the srvc status key of the specified srvc status title  
		select srvc_status.status_key 
		  from cl_cf_srvc_statuses as srvc_status
		 where srvc_status.srvc_id = srvc_type_id_
		   and srvc_status.title = srvc_status_title_
		  into srvc_status_key_; 
		 
		raise notice 'org_id_ %',org_id_;
		raise notice 'srvc_type_id_ %',srvc_type_id_;
		raise notice 'srvc_status_key_ %',srvc_status_key_;
	
		filter_creation_date = array( select json_array_elements_text(json_extract_path(filter_,'creation_srvc_req_date')) )::text[];
		if cardinality(filter_creation_date) > 0 then
		 	filter_from_date =  filter_creation_date[1]::timestamp;
			filter_to_date   =  filter_creation_date[2]::timestamp;
		end if;

		if tms_hlpr_is_org_srvc_prvdr(logged_in_org_id_) then
			customer_access_ = 1;
			-- Extract specific keys from settings_data where setting_type is SP_CUSTOM_FIELDS and srvc_type_id matches
			select jsonb_build_object(
					'is_cstm_fields_dynamic', org_settings.settings_data->>'is_custom_fields_dynamic',
					'cust_fields_json', org_settings.settings_data->>'sp_cust_fields_json',
					'lambda_arn', org_settings.settings_data->>'sp_cust_fields_dynamic_form_lambda_arn'
				)
			  from cl_tx_orgs_settings as org_settings
			 where org_settings.org_id = logged_in_org_id_
			   and org_settings.settings_type = 'SP_CUSTOM_FIELDS'
			   and srvc_type_id_ = any(array(select json_array_elements_text(org_settings.settings_data->'srvc_type_id'))::int[])
			  into custom_fields_config_data;
		else
			select jsonb_build_object( 
				'is_cstm_fields_dynamic',srvc_type.form_data->>'srvc_is_cust_fields_dynamic',
				'cust_fields_json',srvc_type.form_data->>'srvc_cust_fields_json',
				'lambda_arn',srvc_type.form_data->>'srvc_cust_fields_dynamic_form_lambda_arn'
			)
			  from cl_cf_service_types as srvc_type
			 where srvc_type.service_type_id = srvc_type_id_
			  into custom_fields_config_data;
		end if;

	    batch_data_ = array_to_json(array(  
						 select jsonb_build_object( 
								   		'tms_display_code', srvc_req.display_code,
								   		'is_frm_frontend', true,
								   		'srvc_req_id',srvc_req.db_id,
								   		'srvc_type_id',srvc_req.srvc_type_id,
										'form_data',srvc_req.form_data
								   )
						   from cl_tx_srvc_req as srvc_req
						  where (
						  			srvc_req.org_id = logged_in_org_id_
						  			or srvc_req.srvc_prvdr = logged_in_org_id_ 
						  		)
							and tms_hlpr_check_usr_accesibility_to_srvc_req(
							  	  	srvc_req,
							  	  	srvc_type_id_vs_authorities,
							  	  	usr_has_access_to_srvc_requests,
							  	  	usr_has_authority_fr_srvc_requests,
							  	  	filter_locations,
									false,
							  	  	logged_in_org_id_,
							  	  	usr_id_,
							  	  	is_org_srvc_prvdr_
								)
						 	and srvc_req.srvc_type_id = srvc_type_id_ 
						 	and srvc_req.status = srvc_status_key_
						 	and srvc_req.is_deleted is not true  
						 	and DATE((srvc_req.c_meta).time) >= DATE(filter_from_date)  
						    and DATE((srvc_req.c_meta).time) <= DATE(filter_to_date) 
						  group by srvc_req.db_id
						  order by srvc_req.db_id desc
					));
		
		resp_data_ = json_build_object(
				'batch_data', batch_data_,
				'srvc_type_id',srvc_type_id_,
				'custom_fields_config_data',custom_fields_config_data
			);
		return json_build_object('status',true,'code','success','data',resp_data_);
	END;
$function$
;
