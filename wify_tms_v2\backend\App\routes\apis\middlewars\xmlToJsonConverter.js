const { callLambdaFn } = require('../../../api_models/utils/lambda_helpers');
const HttpStatus = require('http-status-codes');

/**
 * Custom body parser for XML content that processes XML before standard body-parser
 * This middleware should be used before the standard body-parser middleware
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const xmlBodyParser = async (req, res, next) => {
    const contentType = req.get('Content-Type') || '';
    const isXmlRequest =
        contentType.includes('application/xml') ||
        contentType.includes('text/xml') ||
        contentType.includes('xml');

    if (!isXmlRequest) {
        return next();
    }

    console.log('xmlBodyParser :: Detected XML request, processing...');

    try {
        let rawBody = '';
        req.setEncoding('utf8');

        req.on('data', (chunk) => {
            rawBody += chunk;
        });

        req.on('end', async () => {
            try {
                if (!rawBody || rawBody.trim() === '') {
                    return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                        status: false,
                        message: 'Empty XML body received',
                        data: {},
                    });
                }

                // Call XML to JSON conversion lambda
                const lambdaParams = {
                    FunctionName:
                        process.env.XML_TO_JSON_LAMBDA_ARN ||
                        'tms_wrapper_convert_xml_to_json',
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({
                        body: rawBody,
                    }),
                };

                const lambdaResponse = await callLambdaFn(lambdaParams);

                if (lambdaResponse.StatusCode !== 200) {
                    console.log(
                        'xmlBodyParser :: Lambda invocation failed:',
                        lambdaResponse
                    );
                    return res
                        .status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
                        .send({
                            status: false,
                            message: 'Failed to convert XML to JSON',
                            data: {},
                        });
                }

                const lambdaResult = JSON.parse(lambdaResponse.Payload);

                if (lambdaResult.statusCode !== 200) {
                    console.log(
                        'xmlBodyParser :: XML conversion failed:',
                        lambdaResult
                    );
                    return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                        status: false,
                        message: 'Invalid XML format',
                        data: lambdaResult.body
                            ? JSON.parse(lambdaResult.body)
                            : {},
                    });
                }

                // Extract the converted JSON from lambda response
                const convertedJson = JSON.parse(lambdaResult.body);

                // Set the converted JSON as the request body
                req.body = convertedJson;

                // Update content-type header to indicate JSON
                req.headers['content-type'] = 'application/json';

                console.log(
                    'xmlBodyParser :: Successfully converted XML to JSON'
                );
                next();
            } catch (conversionError) {
                console.log(
                    'xmlBodyParser :: Conversion error:',
                    conversionError
                );
                return res
                    .status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
                    .send({
                        status: false,
                        message: 'Error processing XML conversion',
                        data: {},
                    });
            }
        });

        req.on('error', (error) => {
            console.log('xmlBodyParser :: Request error:', error);
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: 'Error reading request body',
                data: {},
            });
        });
    } catch (error) {
        console.log('xmlBodyParser :: Middleware error:', error);
        return res.status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Internal server error during XML processing',
            data: {},
        });
    }
};

module.exports = xmlBodyParser;
